package main

import (
	"com/learn3d/myTest/entity"

	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

func main() {
	// 初始化数据库连接
	dsn := "root:1234@tcp(127.0.0.1:3306)/testorm?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn))
	if err != nil {
		panic(err)
	}

	// 创建生成器实例
	g := gen.NewGenerator(gen.Config{
		OutPath:      "../../entity/query",                          // 生成代码的输出目录
		ModelPkgPath: "../../entity",                                // 模型代码的输出目录
		Mode:         gen.WithDefaultQuery | gen.WithQueryInterface, // 生成模式
		WithUnitTest: true,                                          // 生成单元测试代码
	})

	// 使用数据库连接
	g.UseDB(db)

	g.ApplyBasic(
		&entity.User{},
	)

	// 生成代码
	g.Execute()
}
