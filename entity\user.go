package entity

import (
	"time"

	"gorm.io/gorm"
)

// User 对应数据库 tb_user 表
type User struct {
	ID        uint           `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Name      string         `gorm:"type:varchar(50);not null;column:name" json:"name"`
	Phone     string         `gorm:"type:varchar(20);not null;unique;column:phone" json:"phone"`
	Password  string         `gorm:"type:varchar(100);not null;column:password" json:"-"`
	CreatedAt time.Time      `gorm:"autoCreateTime;column:create_time" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;column:update_time" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"` // 软删除字段
}

// TableName 自定义表名
func (User) TableName() string {
	return "tb_user"
}
